<?php
require '../../includes/config.php';
require '../../includes/MarkdownParser.php';

// Function to generate AI response using GitHub AI API
function generateAIResponse($userMessage, $systemMessage, $model) {
    // Get GitHub token from config

    $url = "https://models.github.ai/inference/chat/completions";

    $data = [
        "messages" => [
            [
                "role" => "system",
                "content" => "$systemMessage"
            ],
            [
                "role" => "user",
                "content" => $userMessage
            ]
        ],
        "model" => "$model",
        "temperature" => 1,
        "max_tokens" => 4096,
        "top_p" => 1
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json",
        "Authorization: Bearer ****************************************"
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        $responseData = json_decode($response, true);
        if (isset($responseData['choices'][0]['message']['content'])) {
            return $responseData['choices'][0]['message']['content'];
            
        }
    }

    // Fallback responses if API fails
    $fallbackResponses = [
        "That sounds like an exciting travel destination! Would you like specific recommendations for activities or accommodations?",
        "I'd be happy to help plan your trip. What specific aspects would you like to know more about?",
        "Great choice! Have you considered the best time of year to visit this location?",
        "I can help you create an itinerary. What's your preferred travel style - adventurous, relaxed, or cultural?",
        "That's an interesting destination! Would you like to know about local customs and traditions?"
    ];

    return $fallbackResponses[array_rand($fallbackResponses)];
}

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['userId'];
$chatId = isset($_POST['chatId']) ? intval($_POST['chatId']) : 0;
$messageType = isset($_POST['messageType']) ? $_POST['messageType'] : 'user';
$content = isset($_POST['content']) ? $_POST['content'] : '';

// Validate inputs
if ($chatId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid chat ID']);
    exit;
}

if (empty($content)) {
    echo json_encode(['success' => false, 'message' => 'Message content cannot be empty']);
    exit;
}

if (!in_array($messageType, ['user', 'ai'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid message type']);
    exit;
}

// Sanitize input
$content = filter_var($content, FILTER_SANITIZE_STRING);
// Verify that the chat belongs to the user
$chatQuery = "SELECT id, title FROM chats WHERE id = ? AND user_id = ?";
$chatStmt = $db->prepare($chatQuery);
$chatStmt->bind_param("ii", $chatId, $userId);
$chatStmt->execute();
$chatResult = $chatStmt->get_result();

if ($chatResult->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Chat not found or access denied']);
    exit;
}

$chat = $chatResult->fetch_assoc();

// Insert the message
$query = "INSERT INTO messages (chat_id, user_id, message_type, content) VALUES (?, ?, ?, ?)";
$stmt = $db->prepare($query);
$stmt->bind_param("iiss", $chatId, $userId, $messageType, $content);

if ($stmt->execute()) {
    $messageId = $db->insert_id;
    
    // Update chat's updated_at timestamp
    $updateChatQuery = "UPDATE chats SET updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $updateStmt = $db->prepare($updateChatQuery);
    $updateStmt->bind_param("i", $chatId);
    $updateStmt->execute();
    
    // If this is the first user message, update the chat title
    if ($messageType === 'user') {
        $countQuery = "SELECT COUNT(*) as count FROM messages WHERE chat_id = ? AND message_type = 'user'";
        $countStmt = $db->prepare($countQuery);
        $countStmt->bind_param("i", $chatId);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $count = $countResult->fetch_assoc()['count'];
        
        if ($count == 1) { // First user message
            
            $newTitle = strlen($content) > 30 ? substr($content, 0, 30) . '...' : $content; // Truncate title if it's too long
            $titleQuery = "UPDATE chats SET title = ? WHERE id = ?";
            $titleStmt = $db->prepare($titleQuery);
            $titleStmt->bind_param("si", $newTitle, $chatId);
            $titleStmt->execute();
            $titleStmt->close();
        }
        $countStmt->close();

        // Generate AI response automatically for user messages
        $aiResponse = generateAIResponse($content, "You are TravelAI, a helpful travel assistant. Provide helpful, friendly, and informative responses about travel planning, destinations, accommodations, and travel tips. You can use markdown formatting in your responses including **bold**, *italic*, `code`, lists, links, and other markdown syntax to make your responses more readable and organized.", "openai/gpt-4o");

        if ($aiResponse) {
            // Insert AI response into database
            $aiQuery = "INSERT INTO messages (chat_id, user_id, message_type, content) VALUES (?, ?, 'ai', ?)";
            $aiStmt = $db->prepare($aiQuery);
            $aiStmt->bind_param("iis", $chatId, $userId, $aiResponse);
            $aiStmt->execute();
            $aiStmt->close();
        }
    }

    echo json_encode([
        'success' => true,
        'messageId' => $messageId,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    $updateStmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Error saving message']);
}

$stmt->close();
$chatStmt->close();
?>
