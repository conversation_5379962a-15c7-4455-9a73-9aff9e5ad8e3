<?php
    require '../../includes/config.php';

    $email = $_POST['email'];
    $password = $_POST['password'];
    //sanitize input
    $email = filter_var($email, FILTER_SANITIZE_EMAIL);
    $password = filter_var($password, FILTER_SANITIZE_STRING);     //sanitize input
    // check if user exists
    $query = "SELECT * FROM user WHERE email = '$email'";
    $result = $db->query($query);
    if ($result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'User already exists']);;
        exit;
    }
    // hash password
    $password = password_hash($password, PASSWORD_DEFAULT);
    $query = "INSERT INTO user (email, password) VALUES ('$email', '$password')";
    $result = $db->query($query);

    if ($result) {
        $_SESSION['email'] = $email; // Store user in session for future use (e.g., chat page)
        $_SESSION['userId'] = $db->insert_id; // Store user ID in session for future use (e.g., chat page)        
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error creating user']);
    }
?>