<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelAI Markdown Parser Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0f0f0f',
                        'dark-card': '#1a1a1a',
                        'red-accent': '#ff4444',
                        'red-dark': '#cc3333',
                        'red-light': '#ff6666'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-dark-bg text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-red-accent">TravelAI Markdown Parser Test</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input Section -->
            <div class="bg-dark-card p-6 rounded-lg">
                <h2 class="text-xl font-bold mb-4">Markdown Input</h2>
                <textarea id="markdownInput" class="w-full h-96 p-4 bg-dark-bg border border-gray-700 rounded text-white font-mono text-sm" placeholder="Enter markdown here...">
# Travel Planning Guide

Welcome to **TravelAI**! Here's what I can help you with:

## Popular Destinations

1. **Paris, France** - The city of lights
2. **Tokyo, Japan** - Modern meets traditional
3. **New York, USA** - The city that never sleeps

### Things to Consider

- Budget planning
- *Best time to visit*
- Local customs and `language barriers`

> "Travel is the only thing you buy that makes you richer." - Anonymous

---

### Code Example
```javascript
const destination = "Paris";
console.log(`Planning trip to ${destination}`);
```

For more information, visit [TravelAI](https://example.com).

**Happy travels!** ✈️
                </textarea>
                <button id="parseBtn" class="mt-4 px-6 py-2 bg-red-accent hover:bg-red-dark rounded font-bold">
                    Parse Markdown
                </button>
            </div>
            
            <!-- Output Section -->
            <div class="bg-dark-card p-6 rounded-lg">
                <h2 class="text-xl font-bold mb-4">Parsed Output</h2>
                <div id="parsedOutput" class="bg-dark-bg p-4 rounded border border-gray-700 h-96 overflow-y-auto">
                    <p class="text-gray-400">Click "Parse Markdown" to see the result...</p>
                </div>
            </div>
        </div>
        
        <!-- Test Cases -->
        <div class="mt-8 bg-dark-card p-6 rounded-lg">
            <h2 class="text-xl font-bold mb-4">Quick Test Cases</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="headers">
                    Headers Test
                </button>
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="formatting">
                    Text Formatting
                </button>
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="lists">
                    Lists & Links
                </button>
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="code">
                    Code Blocks
                </button>
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="quotes">
                    Quotes & Rules
                </button>
                <button class="test-case px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm" data-test="mixed">
                    Mixed Content
                </button>
            </div>
        </div>
    </div>

    <script src="includes/js/markdownParser.js"></script>
    <script>
        const testCases = {
            headers: `# Main Title
## Subtitle
### Section
#### Subsection
##### Small Header
###### Tiny Header`,
            
            formatting: `This is **bold text** and this is *italic text*.
You can also use __bold__ and _italic_ like this.
Here's some \`inline code\` in a sentence.`,
            
            lists: `Unordered list:
- First item
- Second item
- Third item

Ordered list:
1. First step
2. Second step
3. Third step

Check out [Google](https://google.com) for more info.`,
            
            code: `Here's a code block:

\`\`\`javascript
function greet(name) {
    console.log("Hello, " + name + "!");
}
greet("TravelAI");
\`\`\`

And some \`inline code\` here.`,
            
            quotes: `> This is a blockquote
> It can span multiple lines

---

Here's a horizontal rule above.`,
            
            mixed: `# Travel Itinerary

## Day 1: **Paris**

- Visit the *Eiffel Tower*
- Walk along the \`Seine River\`
- Dinner at [Le Jules Verne](https://example.com)

> "Paris is always a good idea." - Audrey Hepburn

### Transportation
\`\`\`
Metro Line 6: Bir-Hakeim station
Walking time: 5 minutes
\`\`\`

---

**Total budget:** €150`
        };

        document.getElementById('parseBtn').addEventListener('click', function() {
            const input = document.getElementById('markdownInput').value;
            const output = document.getElementById('parsedOutput');
            
            if (window.markdownParser) {
                const parsed = window.markdownParser.parseForDisplay(input);
                output.innerHTML = parsed;
            } else {
                output.innerHTML = '<p class="text-red-400">Markdown parser not loaded!</p>';
            }
        });

        document.querySelectorAll('.test-case').forEach(button => {
            button.addEventListener('click', function() {
                const testType = this.getAttribute('data-test');
                const testContent = testCases[testType];
                document.getElementById('markdownInput').value = testContent;
                document.getElementById('parseBtn').click();
            });
        });

        // Auto-parse on page load
        window.addEventListener('load', function() {
            document.getElementById('parseBtn').click();
        });
    </script>
</body>
</html>
