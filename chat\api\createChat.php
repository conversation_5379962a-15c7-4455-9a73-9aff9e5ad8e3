<?php
require '../../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['userId'];
$title = isset($_POST['title']) ? $_POST['title'] : 'New Travel Chat';

// Sanitize input
$title = filter_var($title, FILTER_SANITIZE_STRING);

// Insert new chat
$query = "INSERT INTO chats (user_id, title) VALUES (?, ?)";
$stmt = $db->prepare($query);
$stmt->bind_param("is", $userId, $title);

if ($stmt->execute()) {
    $chatId = $db->insert_id;
    echo json_encode([
        'success' => true, 
        'chatId' => $chatId,
        'title' => $title,
        'createdAt' => date('Y-m-d H:i:s')
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Error creating chat']);
}

$stmt->close();
?>
