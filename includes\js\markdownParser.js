/**
 * TravelAI Markdown Parser
 * A lightweight markdown parser for chat messages
 */

class MarkdownParser {
    constructor() {
        this.rules = [
            // Headers (# ## ###)
            {
                pattern: /^(#{1,6})\s+(.+)$/gm,
                replacement: (match, hashes, text) => {
                    const level = hashes.length;
                    return `<h${level} class="text-${level === 1 ? '2xl' : level === 2 ? 'xl' : 'lg'} font-bold mb-2 mt-4">${text.trim()}</h${level}>`;
                }
            },
            
            // Code blocks (```)
            {
                pattern: /```(\w+)?\n([\s\S]*?)```/g,
                replacement: (match, language, code) => {
                    const lang = language ? ` data-language="${language}"` : '';
                    return `<div class="bg-gray-800 rounded-lg p-4 my-3 overflow-x-auto">
                        <pre class="text-sm text-gray-300"${lang}><code>${this.escapeHtml(code.trim())}</code></pre>
                    </div>`;
                }
            },
            
            // Inline code (`)
            {
                pattern: /`([^`]+)`/g,
                replacement: '<code class="bg-gray-800 px-2 py-1 rounded text-sm text-red-300">$1</code>'
            },
            
            // Bold (**text** or __text__)
            {
                pattern: /\*\*([^*]+)\*\*|__([^_]+)__/g,
                replacement: '<strong class="font-bold text-white">$1$2</strong>'
            },
            
            // Italic (*text* or _text_)
            {
                pattern: /\*([^*]+)\*|_([^_]+)_/g,
                replacement: '<em class="italic text-gray-200">$1$2</em>'
            },
            
            // Links [text](url)
            {
                pattern: /\[([^\]]+)\]\(([^)]+)\)/g,
                replacement: '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-red-accent hover:text-red-light underline">$1</a>'
            },
            
            // Unordered lists (- or *)
            {
                pattern: /^[\s]*[-*]\s+(.+)$/gm,
                replacement: '<li class="ml-4 mb-1">• $1</li>'
            },
            
            // Ordered lists (1. 2. etc.)
            {
                pattern: /^[\s]*(\d+)\.\s+(.+)$/gm,
                replacement: '<li class="ml-4 mb-1">$1. $2</li>'
            },
            
            // Blockquotes (>)
            {
                pattern: /^>\s+(.+)$/gm,
                replacement: '<blockquote class="border-l-4 border-red-accent pl-4 py-2 my-2 bg-gray-800 italic">$1</blockquote>'
            },
            
            // Horizontal rule (---)
            {
                pattern: /^---+$/gm,
                replacement: '<hr class="border-gray-600 my-4">'
            },
            
            // Line breaks (double space + newline or double newline)
            {
                pattern: /  \n|\n\n/g,
                replacement: '<br>'
            }
        ];
    }

    /**
     * Escape HTML characters to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Wrap list items in proper ul/ol tags
     */
    wrapLists(html) {
        // Wrap consecutive bullet list items
        html = html.replace(/((<li class="ml-4 mb-1">• .+<\/li>\s*)+)/g, '<ul class="my-2">$1</ul>');
        
        // Wrap consecutive numbered list items
        html = html.replace(/((<li class="ml-4 mb-1">\d+\. .+<\/li>\s*)+)/g, '<ol class="my-2">$1</ol>');
        
        return html;
    }

    /**
     * Parse markdown text and return HTML
     */
    parse(markdown) {
        if (!markdown || typeof markdown !== 'string') {
            return '';
        }

        let html = markdown;

        // Apply all markdown rules
        this.rules.forEach(rule => {
            if (typeof rule.replacement === 'function') {
                html = html.replace(rule.pattern, rule.replacement.bind(this));
            } else {
                html = html.replace(rule.pattern, rule.replacement);
            }
        });

        // Wrap list items in proper containers
        html = this.wrapLists(html);

        // Convert remaining single newlines to <br> tags
        html = html.replace(/\n/g, '<br>');

        return html.trim();
    }

    /**
     * Parse markdown and return safe HTML for display
     */
    parseForDisplay(markdown) {
        const parsed = this.parse(markdown);
        
        // Additional safety: ensure we don't have any unclosed tags
        // This is a basic implementation - for production, consider using a proper HTML sanitizer
        return parsed;
    }
}

// Create global instance
window.markdownParser = new MarkdownParser();

// Export for use in other modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkdownParser;
}
