<?php include '../includes/header.php';
if (isset($_SESSION['userId']) && isset($_SESSION['email'])) {
    header('Location: ../chat');
    exit;
}
?>
<body class="bg-dark-bg text-white font-audiowide min-h-screen flex items-center justify-center py-8">
    <div class="max-w-md w-full mx-4">
        <div class="bg-dark-card p-8 rounded-2xl border border-gray-800">
            <!-- Logo -->
            <div class="text-center mb-8">
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div class="text-red-accent text-3xl">✈️</div>
                    <span class="text-2xl font-bold">TravelAI</span>
                </div>
                <h1 class="text-2xl font-bold">Create Account</h1>
                <p class="text-gray-400 mt-2">Join the future of travel planning</p>
            </div>

            <!-- Register Form -->
            <form id="registerForm" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium mb-2">Email Address</label>
                    <input type="email" id="email" name="email" required
                           class="w-full px-4 py-3 bg-dark-bg border border-gray-700 rounded-lg focus:border-red-accent focus:outline-none transition-colors">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium mb-2">Password</label>
                    <input type="password" id="password" name="password" required
                           class="w-full px-4 py-3 bg-dark-bg border border-gray-700 rounded-lg focus:border-red-accent focus:outline-none transition-colors">
                </div>

             

                <div class="flex items-start">
                    <input type="checkbox" id="terms" required
                           class="w-4 h-4 mt-1 text-red-accent bg-dark-bg border-gray-700 rounded focus:ring-red-accent">
                    <label for="terms" class="ml-2 text-sm text-gray-400">
                        I agree to the <a href="#" class="text-red-accent hover:text-red-dark transition-colors">Terms of Service</a> 
                        and <a href="#" class="text-red-accent hover:text-red-dark transition-colors">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" 
                        class="w-full py-3 bg-red-accent hover:bg-red-dark transition-colors rounded-lg font-bold">
                    Create Account
                </button>
            </form>

            <!-- Divider -->
            <div class="my-6 flex items-center">
                <div class="flex-1 border-t border-gray-700"></div>
                <span class="px-4 text-gray-400 text-sm">or</span>
                <div class="flex-1 border-t border-gray-700"></div>
            </div>

         

            <!-- Sign In Link -->
            <div class="text-center mt-6">
                <p class="text-gray-400">
                    Already have an account? 
                    <a href="../login" class="text-red-accent hover:text-red-dark transition-colors font-bold">Sign in</a>
                </p>
            </div>

            <!-- Back to Home -->
            <div class="text-center mt-4">
                <a href="../" class="text-gray-400 hover:text-white transition-colors text-sm">← Back to Home</a>
            </div>
        </div>
    </div>

    <!-- Message Container -->
    <div id="messageContainer" class="fixed top-4 right-4 max-w-sm"></div>

    <script>
        function showMessage(message, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg shadow-lg ${isError ? 'bg-red-500' : 'bg-green-500'} text-white mb-4`;
            messageDiv.textContent = message;
            
            const container = document.getElementById('messageContainer');
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const email = document.getElementById('email').value;
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            try {
                const response = await fetch('api/createUser.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage('Registration successful! Redirecting...', false);
                    setTimeout(() => {
                        window.location.href = '../chat';
                    }, 1500);
                } else {
                    showMessage('Registration failed: ' + data.message, true);
                }
                
            } catch (error) {
                showMessage('Registration failed: Network or server error', true);
            }
        });
    </script>
<?php include '../includes/footer.php'; ?>