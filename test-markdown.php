<?php
require 'includes/MarkdownParser.php';

$testMarkdown = '# Travel Planning Guide

Welcome to **TravelAI**! Here\'s what I can help you with:

## Popular Destinations

1. **Paris, France** - The city of lights
2. **Tokyo, Japan** - Modern meets traditional
3. **New York, USA** - The city that never sleeps

### Things to Consider

- Budget planning
- *Best time to visit*
- Local customs and `language barriers`

> "Travel is the only thing you buy that makes you richer." - Anonymous

---

### Code Example
```javascript
const destination = "Paris";
console.log(`Planning trip to ${destination}`);
```

For more information, visit [TravelAI](https://example.com).

**Happy travels!** ✈️';

$parser = new MarkdownParser();
$parsedHtml = $parser->parseForDisplay($testMarkdown);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Markdown Parser Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0f0f0f',
                        'dark-card': '#1a1a1a',
                        'red-accent': '#ff4444',
                        'red-dark': '#cc3333',
                        'red-light': '#ff6666'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-dark-bg text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-red-accent">PHP Markdown Parser Test</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Original Markdown -->
            <div class="bg-dark-card p-6 rounded-lg">
                <h2 class="text-xl font-bold mb-4">Original Markdown</h2>
                <pre class="bg-dark-bg p-4 rounded border border-gray-700 text-sm overflow-x-auto"><code><?php echo htmlspecialchars($testMarkdown); ?></code></pre>
            </div>
            
            <!-- Parsed HTML -->
            <div class="bg-dark-card p-6 rounded-lg">
                <h2 class="text-xl font-bold mb-4">Parsed HTML</h2>
                <div class="bg-dark-bg p-4 rounded border border-gray-700 overflow-y-auto">
                    <div class="prose prose-invert max-w-none text-gray-200">
                        <?php echo $parsedHtml; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Raw HTML Output -->
        <div class="mt-8 bg-dark-card p-6 rounded-lg">
            <h2 class="text-xl font-bold mb-4">Raw HTML Output</h2>
            <pre class="bg-dark-bg p-4 rounded border border-gray-700 text-sm overflow-x-auto"><code><?php echo htmlspecialchars($parsedHtml); ?></code></pre>
        </div>
    </div>
</body>
</html>
