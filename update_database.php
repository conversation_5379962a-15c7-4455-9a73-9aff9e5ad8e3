<?php
require 'includes/config.php';

echo "Updating TravelAI database with chat tables...\n";

// Create chats table
$createChatsTable = "
CREATE TABLE IF NOT EXISTS `chats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL DEFAULT 'New Travel Chat',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `chats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

// Create messages table
$createMessagesTable = "
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chat_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message_type` enum('user','ai') NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `chat_id` (`chat_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE,
  CONSTRAINT `messages_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

try {
    // Execute chats table creation
    if ($db->query($createChatsTable)) {
        echo "✓ Chats table created successfully\n";
    } else {
        echo "✗ Error creating chats table: " . $db->error . "\n";
    }
    
    // Execute messages table creation
    if ($db->query($createMessagesTable)) {
        echo "✓ Messages table created successfully\n";
    } else {
        echo "✗ Error creating messages table: " . $db->error . "\n";
    }
    
    echo "\nDatabase update completed!\n";
    echo "You can now use the chat system with database storage.\n";
    
} catch (Exception $e) {
    echo "✗ Error updating database: " . $e->getMessage() . "\n";
}

$db->close();
?>
