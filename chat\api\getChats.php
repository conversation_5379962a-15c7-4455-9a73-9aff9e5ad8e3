<?php
require '../../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['userId'];

// Get user's chats ordered by most recent
$query = "SELECT id, title, created_at, updated_at FROM chats WHERE user_id = ? ORDER BY updated_at DESC";
$stmt = $db->prepare($query);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

$chats = [];
while ($row = $result->fetch_assoc()) {
    $chats[] = [
        'id' => $row['id'],
        'title' => $row['title'],
        'createdAt' => $row['created_at'],
        'updatedAt' => $row['updated_at']
    ];
}

echo json_encode(['success' => true, 'chats' => $chats]);

$stmt->close();
?>
