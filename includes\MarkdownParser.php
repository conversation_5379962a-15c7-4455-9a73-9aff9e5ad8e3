<?php
/**
 * TravelAI Markdown Parser (PHP Version)
 * A lightweight markdown parser for chat messages
 */

class MarkdownParser {
    private $rules;

    public function __construct() {
        $this->rules = [
            // Headers (# ## ###)
            [
                'pattern' => '/^(#{1,6})\s+(.+)$/m',
                'replacement' => function($matches) {
                    $level = strlen($matches[1]);
                    $sizeClass = $level === 1 ? '2xl' : ($level === 2 ? 'xl' : 'lg');
                    return "<h{$level} class=\"text-{$sizeClass} font-bold mb-2 mt-4\">" . trim($matches[2]) . "</h{$level}>";
                }
            ],
            
            // Code blocks (```)
            [
                'pattern' => '/```(\w+)?\n([\s\S]*?)```/s',
                'replacement' => function($matches) {
                    $language = isset($matches[1]) ? " data-language=\"{$matches[1]}\"" : '';
                    $code = htmlspecialchars(trim($matches[2]), ENT_QUOTES, 'UTF-8');
                    return "<div class=\"bg-gray-800 rounded-lg p-4 my-3 overflow-x-auto\">
                        <pre class=\"text-sm text-gray-300\"{$language}><code>{$code}</code></pre>
                    </div>";
                }
            ],
            
            // Inline code (`)
            [
                'pattern' => '/`([^`]+)`/',
                'replacement' => '<code class="bg-gray-800 px-2 py-1 rounded text-sm text-red-300">$1</code>'
            ],
            
            // Bold (**text** or __text__)
            [
                'pattern' => '/\*\*([^*]+)\*\*|__([^_]+)__/',
                'replacement' => '<strong class="font-bold text-white">$1$2</strong>'
            ],
            
            // Italic (*text* or _text_)
            [
                'pattern' => '/\*([^*]+)\*|_([^_]+)_/',
                'replacement' => '<em class="italic text-gray-200">$1$2</em>'
            ],
            
            // Links [text](url)
            [
                'pattern' => '/\[([^\]]+)\]\(([^)]+)\)/',
                'replacement' => '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-red-accent hover:text-red-light underline">$1</a>'
            ],
            
            // Unordered lists (- or *)
            [
                'pattern' => '/^[\s]*[-*]\s+(.+)$/m',
                'replacement' => '<li class="ml-4 mb-1">• $1</li>'
            ],
            
            // Ordered lists (1. 2. etc.)
            [
                'pattern' => '/^[\s]*(\d+)\.\s+(.+)$/m',
                'replacement' => '<li class="ml-4 mb-1">$1. $2</li>'
            ],
            
            // Blockquotes (>)
            [
                'pattern' => '/^>\s+(.+)$/m',
                'replacement' => '<blockquote class="border-l-4 border-red-accent pl-4 py-2 my-2 bg-gray-800 italic">$1</blockquote>'
            ],
            
            // Horizontal rule (---)
            [
                'pattern' => '/^---+$/m',
                'replacement' => '<hr class="border-gray-600 my-4">'
            ]
        ];
    }

    /**
     * Wrap list items in proper ul/ol tags
     */
    private function wrapLists($html) {
        // Wrap consecutive bullet list items
        $html = preg_replace('/((<li class="ml-4 mb-1">• .+<\/li>\s*)+)/', '<ul class="my-2">$1</ul>', $html);
        
        // Wrap consecutive numbered list items
        $html = preg_replace('/((<li class="ml-4 mb-1">\d+\. .+<\/li>\s*)+)/', '<ol class="my-2">$1</ol>', $html);
        
        return $html;
    }

    /**
     * Parse markdown text and return HTML
     */
    public function parse($markdown) {
        if (!$markdown || !is_string($markdown)) {
            return '';
        }

        $html = $markdown;

        // Apply all markdown rules
        foreach ($this->rules as $rule) {
            if (is_callable($rule['replacement'])) {
                $html = preg_replace_callback($rule['pattern'], $rule['replacement'], $html);
            } else {
                $html = preg_replace($rule['pattern'], $rule['replacement'], $html);
            }
        }

        // Wrap list items in proper containers
        $html = $this->wrapLists($html);

        // Convert line breaks (double space + newline or double newline)
        $html = preg_replace('/  \n|\n\n/', '<br>', $html);
        
        // Convert remaining single newlines to <br> tags
        $html = preg_replace('/\n/', '<br>', $html);

        return trim($html);
    }

    /**
     * Parse markdown and return safe HTML for display
     */
    public function parseForDisplay($markdown) {
        return $this->parse($markdown);
    }

    /**
     * Static method for easy access
     */
    public static function parseText($markdown) {
        $parser = new self();
        return $parser->parseForDisplay($markdown);
    }
}
?>
