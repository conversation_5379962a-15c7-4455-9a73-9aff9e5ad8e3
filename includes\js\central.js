  
  
  
   function showMessage(message, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg shadow-lg ${isError ? 'bg-red-500' : 'bg-green-500'} text-white mb-4`;
            messageDiv.textContent = message;
            
            const container = document.getElementById('messageContainer');
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
  document.getElementById('logoutBtn').addEventListener('click', function() {
            fetch('/travelai/chat/api/logout.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Logout successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = '../login';
                    }, 1500);
                } else {
                    showMessage(data.message || 'Logout failed', true);
                }
            })
            .catch(error => {
                showMessage('An error occurred. Please try again.', true);
                console.error('Error:', error);
            });
        });
