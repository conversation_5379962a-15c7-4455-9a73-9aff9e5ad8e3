# GitHub AI API Setup Instructions

## How to get your GitHub AI API Token

1. Go to [GitHub Settings](https://github.com/settings/tokens)
2. Click "Generate new token" → "Generate new token (classic)"
3. Give it a name like "TravelAI Chat"
4. Select appropriate scopes (you may need to check with GitHub AI documentation)
5. Click "Generate token"
6. Copy the generated token

## Configure TravelAI

1. Open `includes/config.php`
2. Find the line: `define('GITHUB_AI_TOKEN', 'YOUR_GITHUB_TOKEN_HERE');`
3. Replace `YOUR_GITHUB_TOKEN_HERE` with your actual GitHub token
4. Save the file

Example:
```php
define('GITHUB_AI_TOKEN', 'ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');
```

## Test the Integration

1. Open your TravelAI chat application
2. Send a message
3. You should receive an AI-powered response from GitHub's GPT-4o model

## Troubleshooting

- If you get fallback responses, check your token is correct
- Make sure your server can make outbound HTTPS requests
- Check PHP error logs for any cURL errors
